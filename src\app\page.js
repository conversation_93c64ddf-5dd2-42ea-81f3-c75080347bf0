'use client';

import React, { useState } from 'react';
import './globals.css';
import { useGameLogic } from '../hooks/useGameLogic';
import PuzzleContainer from '../components/PuzzleContainer';
import { gameData } from '../data/gameData';

export default function Home() {
  // Hooks must be called unconditionally and in the same order
  const gameLogic = useGameLogic();

  const [userAnswers, setUserAnswers] = useState({});

  const {
    currentPuzzle,
    gameRound,
    annaMessage,
    hintVisible,
    isVictory,
    checkAnswer,
    restartGame,
    toggleHint,
    getCurrentHint
  } = gameLogic;

  const handleSubmit = (puzzleNumber, userAnswer) => {
    checkAnswer(puzzleNumber, userAnswer);
  };

  // Prepare puzzleData based on currentPuzzle and gameRound
  const getPuzzleData = () => {
    const roundIndex = (gameRound - 1) % 3;

    switch (currentPuzzle) {
      case 1:
        return gameData.binaryStrings[roundIndex];
      case 2:
        return gameData.hiddenCodes[roundIndex];
      case 3:
        return gameData.triviaQuestions[roundIndex];
      case 4:
        return gameData.qrData;
      case 5:
        return gameData.codeSnippets[roundIndex];
      case 6:
        return gameData.gateCombinations[roundIndex];
      case 7:
        return gameData.asciiMessages[roundIndex];
      case 8:
        return gameData.localhostRiddle;
      default:
        return null;
    }
  };

  const puzzleData = getPuzzleData();

  return (
    <div className="game-container">
      {/* Header */}
      <header className="game-header">
        <h1 className="game-title">TECH ESCAPE ROOM</h1>
        <div className="system-status">
          <span className="status-indicator"></span>
          <span className="status-text">A.N.N.A. SYSTEM ACTIVE</span>
          <div className="round-indicator">
            <span className="round-text">ROUND <span id="round-number">{gameRound}</span></span>
          </div>
        </div>
      </header>

      {/* A.N.N.A. Dialogue System */}
      <div className="anna-dialogue" id="anna-dialogue">
        <div className="anna-avatar">
          <div className="avatar-circle">
            <span className="avatar-text">A.N.N.A.</span>
          </div>
        </div>
        <div className="dialogue-content">
          <p id="anna-message">{annaMessage}</p>
        </div>
      </div>

      {/* Game Progress Bar */}
      <div className="progress-container">
        <div className="progress-bar">
          <div
            className="progress-fill"
            id="progress-fill"
            style={{ width: ((currentPuzzle - 1) / 8) * 100 + '%' }}
          ></div>
        </div>
        <div className="progress-text">
          <span id="current-puzzle">{isVictory ? 'Complete' : `Puzzle ${currentPuzzle}`}</span> of 8
        </div>
      </div>

      {/* Main Game Area */}
      <main className="game-main">
        {!isVictory ? (
          <PuzzleContainer
            puzzleNumber={currentPuzzle}
            puzzleData={puzzleData}
            gameRound={gameRound}
            onSubmit={handleSubmit}
            userAnswers={userAnswers}
            setUserAnswers={setUserAnswers}
          />
        ) : (
          <div className="puzzle-container active" id="victory-screen">
            <div className="victory-content">
              <h2>CONGRATULATIONS!</h2>
              <p>
                You have successfully escaped A.N.N.A.'s network. Your technical prowess has impressed even an AI. You are free to go... for now.
              </p>
              <button onClick={restartGame}>PLAY AGAIN</button>
            </div>
          </div>
        )}
      </main>

      {/* Hint System */}
      <div className="hint-container" id="hint-container">
        <button className="hint-button" onClick={toggleHint}>💡 HINT</button>
        {hintVisible && (
          <div className="hint-content" id="hint-content">
            {getCurrentHint()}
          </div>
        )}
      </div>
    </div>
  );
}
