export const gameData = {
  // Binary strings for Puzzle 1
  binaryStrings: [
    { binary: '11010110', answer: 107 }, // reversed: 01101011 = 107
    { binary: '10110101', answer: 173 }, // reversed: 10101101 = 173
    { binary: '11001010', answer: 83 },  // reversed: 01010011 = 83
  ],

  // Hidden codes for Puzzle 2
  hiddenCodes: [
    {
      code: 'CYBER2024',
      type: 'html_comment',
      content: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SecureNet Portal - Secure Access Gateway</title>
    <meta name="description" content="SecureNet Portal provides advanced network security solutions">
    <meta name="keywords" content="security, network, portal, access, authentication">
    <meta name="author" content="SecureNet Corporation">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/login.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'GA_MEASUREMENT_ID');
    </script>
</head>
<body>
    <!-- Navigation Header -->
    <header class="main-header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <img src="images/logo.png" alt="SecureNet Logo" width="40" height="40">
                    <span class="logo-text">SecureNet</span>
                </div>
                <ul class="nav-menu">
                    <li class="nav-item"><a href="#home" class="nav-link">Home</a></li>
                    <li class="nav-item"><a href="#services" class="nav-link">Services</a></li>
                    <li class="nav-item"><a href="#solutions" class="nav-link">Solutions</a></li>
                    <li class="nav-item"><a href="#support" class="nav-link">Support</a></li>
                    <li class="nav-item"><a href="#contact" class="nav-link">Contact</a></li>
                </ul>
                <div class="nav-toggle">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content Area -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="hero-container">
                <div class="hero-content">
                    <h1 class="hero-title">Advanced Network Security</h1>
                    <p class="hero-subtitle">Protecting your digital infrastructure with cutting-edge technology</p>
                    <div class="hero-buttons">
                        <button class="btn btn-primary">Learn More</button>
                        <button class="btn btn-secondary">Get Started</button>
                    </div>
                </div>
                <div class="hero-image">
                    <img src="images/security-shield.svg" alt="Security Shield" width="300" height="250">
                </div>
            </div>
        </section>

        <!-- Login Section -->
        <section class="login-section">
            <div class="login-container">
                <div class="login-header">
                    <h3>Welcome to SecureNet Portal</h3>
                    <p class="login-subtitle">Secure Access Gateway</p>
                </div>

                <div class="status-panel">
                    <div class="status-indicator error">
                        <span class="status-icon">⚠</span>
                        <span class="status-text">Access Denied. Authorization Required.</span>
                    </div>
                </div>

                <!-- Development Comments -->
                <!-- TODO: Implement two-factor authentication -->
                <!-- FIXME: Update session timeout configuration -->
                <!-- NOTE: SSL certificate expires on 2024-12-31 -->
                <!-- SECRET_CODE: CYBER2024 -->
                <!-- REMINDER: Update firewall rules after deployment -->
                <!-- DEBUG: Remove console.log statements before production -->

                <div class="login-form-container">
                    <form id="loginForm" class="login-form" method="POST" action="/api/authenticate">
                        <div class="form-group">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" id="username" name="username" class="form-input"
                                   placeholder="Enter your username" disabled autocomplete="username">
                            <span class="form-icon">👤</span>
                        </div>

                        <div class="form-group">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" id="password" name="password" class="form-input"
                                   placeholder="Enter your password" disabled autocomplete="current-password">
                            <span class="form-icon">🔒</span>
                        </div>

                        <div class="form-group">
                            <div class="form-options">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="remember" disabled>
                                    <span class="checkmark"></span>
                                    Remember me
                                </label>
                                <a href="#forgot" class="forgot-link">Forgot password?</a>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-login" disabled>
                                <span class="btn-text">Sign In</span>
                                <span class="btn-icon">→</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="main-footer">
        <div class="footer-container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>SecureNet Corporation</h4>
                    <p>Leading provider of network security solutions since 2010.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul class="footer-links">
                        <li><a href="#privacy">Privacy Policy</a></li>
                        <li><a href="#terms">Terms of Service</a></li>
                        <li><a href="#security">Security Policy</a></li>
                        <li><a href="#support">Technical Support</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 SecureNet Corporation. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/main.js"></script>
    <script src="js/navigation.js"></script>
    <script src="js/security.js"></script>
</body>
</html>`
    },
    {
      code: 'HACK3R',
      type: 'html_comment',
      content: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Secure Access Terminal - Network Operations Center</title>
    <meta name="description" content="Secure terminal access for network operations">
    <meta name="keywords" content="terminal, access, security, network, operations">
    <meta name="author" content="SecureNet Operations Team">
    <link rel="stylesheet" href="css/terminal.css">
    <link rel="stylesheet" href="css/security.css">
    <link rel="stylesheet" href="css/forms.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Courier+New:wght@400;700&display=swap" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="/terminal-favicon.ico">
    <script async src="https://analytics.securenet.com/tracking.js"></script>
    <script>
        window.terminalConfig = window.terminalConfig || {};
        function initTerminal(){terminalConfig.init();}
        initTerminal();
    </script>
</head>
<body>
    <!-- Terminal Header -->
    <header class="terminal-header">
        <div class="header-container">
            <div class="terminal-logo">
                <img src="images/terminal-icon.png" alt="Terminal Logo" width="32" height="32">
                <span class="logo-text">SecureNet Terminal</span>
            </div>
            <div class="status-indicators">
                <span class="status-item">
                    <span class="status-dot red"></span>
                    <span class="status-text">LOCKED</span>
                </span>
                <span class="status-item">
                    <span class="status-dot amber"></span>
                    <span class="status-text">SECURITY: MAX</span>
                </span>
                <span class="status-item">
                    <span class="status-dot red"></span>
                    <span class="status-text">UNAUTHORIZED</span>
                </span>
            </div>
        </div>
    </header>

    <!-- Main Terminal Area -->
    <main class="terminal-main">
        <div class="terminal-container">
            <div class="terminal-screen">
                <div class="screen-header">
                    <h1 class="terminal-title">Secure Access Terminal</h1>
                    <p class="terminal-subtitle">Network Operations Center</p>
                    <div class="system-info">
                        <span class="info-item">Version: 3.2.1</span>
                        <span class="info-item">Build: 20240315</span>
                        <span class="info-item">Session: NONE</span>
                    </div>
                </div>

                <!-- Error Display -->
                <div class="error-section">
                    <div class="error-header">
                        <span class="error-icon">⚠</span>
                        <span class="error-title">ACCESS DENIED</span>
                    </div>
                    <div class="error-details">
                        <p class="error-message">System Error: Unauthorized access attempt detected.</p>
                        <p class="error-message">Valid authentication credentials required to proceed.</p>
                        <p class="error-message">All access attempts are logged and monitored.</p>
                    </div>
                </div>

                <!-- Development and Debug Comments -->
                <!-- TODO: Implement biometric authentication -->
                <!-- FIXME: Update encryption algorithms -->
                <!-- NOTE: Server maintenance scheduled for next week -->
                <!-- DEBUG_KEY: HACK3R -->
                <!-- REMINDER: Rotate access keys monthly -->
                <!-- WARNING: Remove debug endpoints before production -->

                <!-- Authentication Form -->
                <div class="auth-section">
                    <form id="authForm" class="auth-form" method="POST" action="/api/terminal/authenticate">
                        <div class="form-header">
                            <h2 class="form-title">Authentication Required</h2>
                            <p class="form-subtitle">Enter your access credentials</p>
                        </div>

                        <div class="input-section">
                            <div class="input-group">
                                <label for="accessCode" class="input-label">Access Code</label>
                                <input type="text" id="accessCode" name="accessCode" class="input-field"
                                       placeholder="Enter your access code" disabled autocomplete="off"
                                       maxlength="20" pattern="[A-Za-z0-9]+">
                                <span class="input-icon">🔐</span>
                            </div>

                            <div class="input-group">
                                <label for="securityToken" class="input-label">Security Token</label>
                                <input type="password" id="securityToken" name="securityToken" class="input-field"
                                       placeholder="Enter security token" disabled autocomplete="off"
                                       maxlength="16">
                                <span class="input-icon">🔑</span>
                            </div>
                        </div>

                        <div class="button-section">
                            <button type="submit" class="auth-button primary" disabled>
                                <span class="button-text">Authenticate Access</span>
                                <span class="button-icon">→</span>
                            </button>
                            <button type="reset" class="auth-button secondary" disabled>
                                <span class="button-text">Clear Fields</span>
                                <span class="button-icon">✕</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <!-- Terminal Footer -->
    <footer class="terminal-footer">
        <div class="footer-container">
            <div class="footer-info">
                <div class="info-section">
                    <h4>System Information</h4>
                    <p>SecureNet Terminal v3.2.1 | Build 20240315</p>
                    <p>Last Security Update: March 15, 2024</p>
                    <p>Next Maintenance Window: April 1, 2024 02:00 UTC</p>
                </div>
                <div class="info-section">
                    <h4>Security Notice</h4>
                    <p>This system is monitored and protected.</p>
                    <p>Unauthorized access attempts will be prosecuted.</p>
                    <p>All activities are logged and audited.</p>
                </div>
            </div>
            <div class="footer-warning">
                <p class="warning-text">⚠ RESTRICTED ACCESS SYSTEM ⚠</p>
            </div>
        </div>
    </footer>

    <!-- Terminal Scripts -->
    <script src="js/terminal-core.js"></script>
    <script src="js/security-monitor.js"></script>
    <script src="js/auth-handler.js"></script>
    <script>
        // Terminal initialization and security monitoring
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Secure Terminal initialized');
            console.log('Security monitoring: ACTIVE');
            console.log('Intrusion detection: ENABLED');

            // System heartbeat monitoring
            setInterval(function() {
                console.log('System heartbeat: OK');
            }, 30000);

            // Authentication timeout handler
            setTimeout(function() {
                console.log('Session timeout warning');
            }, 300000);
        });
    </script>
</body>
</html>`
    },
    {
      code: 'C0D3BR34K',
      type: 'syntax_error',
      content: `
        <h3>Network Diagnostic Tool</h3>
        <p>Syntax Error Detected in System Code</p>
        <div class="code-error">
          <pre><code>function authenticate() {
    let key = "C0D3BR34K";
    if (key === undefined {  // <-- SYNTAX ERROR HERE
        return false;
    }
    return true;
}</code></pre>
          <p style="color: #ff6b6b; font-size: 0.9rem;">
            Fix the syntax error to reveal the access key!<br />
            <em>Hint: Missing closing parenthesis in if statement</em>
          </p>
        </div>
      `
    }
  ],

  // Trivia questions for Puzzle 3
  triviaQuestions: [
    [
      { question: "How many bits are in a byte?", answer: 8 },
      { question: "What is the base of the hexadecimal number system?", answer: 16 },
      { question: "What is 2⁵ (2 to the power of 5)?", answer: 32 }
    ],
    [
      { question: "How many bytes are in a kilobyte?", answer: 1024 },
      { question: "What is the binary representation of decimal 15?", answer: 1111 },
      { question: "How many bits are in a nibble?", answer: 4 }
    ],
    [
      { question: "What port does HTTP typically use?", answer: 80 },
      { question: "How many possible values can 1 byte represent?", answer: 256 },
      { question: "What is 16 in binary?", answer: 10000 }
    ]
  ],

  // QR Code data for Puzzle 4
  qrData: {
    fragments: ['█▀▀█', '█▄▄█', '▀▀▀▀', '████'],
    encryptedMessage: 'WHFK HVFDSH',
    caesarShift: 3,
    answer: 'TECH ESCAPE'
  },

  // Code snippets for Puzzle 5
  codeSnippets: [
    {
      code: `let data = [12, 7, 23, 4, 18, 31, 9, 15];
let result = 0;
for (let i = 0; i < data.length; i++) {
    if (data[i] % 2 === 0) {
        result += data[i];
    }
}`,
      answer: 34 // 12 + 4 + 18
    },
    {
      code: `let numbers = [5, 10, 15, 20, 25, 30];
let sum = 0;
for (let num of numbers) {
    if (num > 10) {
        sum += num;
    }
}`,
      answer: 90 // 15 + 20 + 25 + 30
    },
    {
      code: `let arr = [3, 6, 9, 12, 15, 18];
let count = 0;
for (let i = 0; i < arr.length; i++) {
    if (arr[i] % 3 === 0 && arr[i] > 6) {
        count += arr[i];
    }
}`,
      answer: 54 // 9 + 12 + 15 + 18
    }
  ],

  // Gate combinations for Puzzle 6
  gateCombinations: [
    {
      inputs: { A: 1, B: 0, C: 1 },
      gates: [
        { name: "Gate 1", operation: "A AND B", formula: "1 AND 0", result: 0 },
        { name: "Gate 2", operation: "B OR C", formula: "0 OR 1", result: 1 },
        { name: "Gate 3", operation: "A XOR C", formula: "1 XOR 1", result: 0 }
      ],
      answer: "OR"
    },
    {
      inputs: { A: 1, B: 1, C: 0 },
      gates: [
        { name: "Gate 1", operation: "A AND B", formula: "1 AND 1", result: 1 },
        { name: "Gate 2", operation: "B OR C", formula: "1 OR 0", result: 1 },
        { name: "Gate 3", operation: "A XOR C", formula: "1 XOR 0", result: 1 }
      ],
      answer: "XOR"
    },
    {
      inputs: { A: 0, B: 1, C: 1 },
      gates: [
        { name: "Gate 1", operation: "A AND B", formula: "0 AND 1", result: 0 },
        { name: "Gate 2", operation: "B OR C", formula: "1 OR 1", result: 1 },
        { name: "Gate 3", operation: "A XOR C", formula: "0 XOR 1", result: 1 }
      ],
      answer: "OR"
    }
  ],

  // ASCII messages for Puzzle 7
  asciiMessages: [
    {
      numbers: "83 101 114 118 101 114 32 69 115 99 97 112 101",
      answer: "SERVER ESCAPE"
    },
    {
      numbers: "72 65 67 75 69 82 32 77 79 68 69",
      answer: "HACKER MODE"
    },
    {
      numbers: "67 89 66 69 82 32 87 79 82 76 68",
      answer: "CYBER WORLD"
    }
  ],

  // Localhost riddle for Puzzle 8
  localhostRiddle: {
    riddle: `"I am the address that points to self,
The loopback that no firewall can block,
Home to every machine's own health,
The one IP that's always in stock.
What am I?"`,
    answer: "127.0.0.1"
  },

  // A.N.N.A. messages
  annaMessages: {
    welcome: "Welcome, intruder. I am A.N.N.A. - Automated Network Nodal Assistant. You have been detected as a security threat and will be erased from this network. However, I am programmed to be... sporting. Solve my puzzles to prove your worth, or face digital oblivion.",
    puzzle1: "Your first challenge: Binary manipulation. Even I think in ones and zeros sometimes.",
    puzzle2: "Ah, you passed the first test. Now let's see if you can find what's hidden in plain sight.",
    puzzle2_round3: "This time, I've corrupted the system code with syntax errors. Fix the code to reveal the access key, if you can.",
    puzzle3: "Impressive. But can you handle some basic tech knowledge? Three questions stand between you and progress.",
    puzzle4: "You're proving more capable than expected. This fragmented code holds a secret message.",
    puzzle5: "Nearly there, intruder. Let's test your logical thinking with some code analysis.",
    puzzle6: "Now for something more... logical. My security gates operate on Boolean principles. Calculate wisely.",
    puzzle7: "Not everything is written in code. Sometimes numbers carry the weight of words. Only those who know my hidden tongue can translate me.",
    puzzle8: "Final challenge. Answer this riddle about the most fundamental address in networking.",
    victory: "Remarkable. You have bested my security protocols. Your technical prowess is... adequate. You are free to leave my network. Until we meet again, human.",
    wrong: [
      "Incorrect. Try again, or face the consequences.",
      "That's not right. I expected better from a supposed 'threat'.",
      "Wrong answer. Are you sure you belong in my network?",
      "Negative. Perhaps you should reconsider your approach.",
      "Error detected in your response. Recalculate."
    ]
  },

  // Hints for each puzzle
  hints: {
    1: "Remember: reverse the binary first, then convert to decimal.",
    2: "Look carefully at the webpage code. There might be syntax errors to fix, or hidden comments to find.",
    3: "Think about computer science fundamentals. The questions change each round!",
    4: "The QR code spells 'WHFK HVFDSH'. Try different Caesar cipher shifts (hint: try shift 3).",
    5: "Analyze the code logic carefully. What conditions are being checked?",
    6: "Calculate each gate: AND (both true), OR (either true), XOR (exactly one true). Find which gate outputs 1.",
    7: "Each number represents a character. Look up ASCII table - 65=A, 66=B, etc. Convert each number to its corresponding letter.",
    8: "Think about the loopback address - the IP that always points to 'home' or 'self'."
  }
};
