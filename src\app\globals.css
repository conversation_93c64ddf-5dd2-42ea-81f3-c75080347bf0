/* Tech Escape Room Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto Mono', monospace;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    color: #00ff41;
    min-height: 100vh;
    overflow-x: hidden;
}

.game-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

/* Header Styles */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 2px solid #00ff41;
    margin-bottom: 30px;
}

.game-title {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    font-weight: 900;
    color: #00ff41;
    text-shadow: 0 0 20px #00ff41;
    letter-spacing: 3px;
}

.system-status {
    display: flex;
    align-items: center;
    gap: 20px;
}

.round-indicator {
    background: rgba(0, 255, 65, 0.1);
    border: 1px solid #00ff41;
    border-radius: 15px;
    padding: 5px 15px;
}

.round-text {
    font-family: 'Orbitron', monospace;
    font-size: 0.8rem;
    font-weight: 700;
    color: #00ff41;
}

.status-indicator {
    width: 12px;
    height: 12px;
    background: #ff0040;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-text {
    font-size: 0.9rem;
    color: #ff0040;
    font-weight: 500;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

/* A.N.N.A. Dialogue System */
.anna-dialogue {
    display: flex;
    gap: 20px;
    background: rgba(0, 255, 65, 0.1);
    border: 1px solid #00ff41;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    animation: glow 3s ease-in-out infinite alternate;
}

@keyframes glow {
    from { box-shadow: 0 0 5px #00ff41; }
    to { box-shadow: 0 0 20px #00ff41, 0 0 30px #00ff41; }
}

.anna-avatar {
    flex-shrink: 0;
}

.avatar-circle {
    width: 60px;
    height: 60px;
    border: 2px solid #00ff41;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 255, 65, 0.2);
}

.avatar-text {
    font-family: 'Orbitron', monospace;
    font-size: 0.8rem;
    font-weight: 700;
    color: #00ff41;
}

.dialogue-content {
    flex: 1;
}

.dialogue-content p {
    line-height: 1.6;
    font-size: 1rem;
}

/* Progress Bar */
.progress-container {
    margin-bottom: 30px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00ff41, #00cc33);
    width: 0%;
    transition: width 0.5s ease;
}

.progress-text {
    text-align: center;
    font-size: 0.9rem;
    color: #cccccc;
}

/* Puzzle Containers */
.puzzle-container {
    display: none;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid #333;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
}

.puzzle-container.active {
    display: block;
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.puzzle-header h2 {
    font-family: 'Orbitron', monospace;
    font-size: 1.8rem;
    color: #00ff41;
    margin-bottom: 15px;
    text-align: center;
}

.puzzle-description {
    text-align: center;
    margin-bottom: 25px;
    color: #cccccc;
}

.puzzle-content {
    max-width: 600px;
    margin: 0 auto;
}

/* Input Styles */
.input-group {
    margin: 20px 0;
    text-align: center;
}

.input-group label {
    display: block;
    margin-bottom: 10px;
    color: #00ff41;
    font-weight: 500;
}

input[type="text"], input[type="number"] {
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid #333;
    border-radius: 5px;
    padding: 12px 15px;
    color: #00ff41;
    font-family: 'Roboto Mono', monospace;
    font-size: 1rem;
    width: 200px;
    margin-right: 10px;
    transition: border-color 0.3s ease;
}

input[type="text"]:focus, input[type="number"]:focus {
    outline: none;
    border-color: #00ff41;
    box-shadow: 0 0 10px rgba(0, 255, 65, 0.3);
}

button {
    background: linear-gradient(45deg, #00ff41, #00cc33);
    border: none;
    border-radius: 5px;
    padding: 12px 25px;
    color: #000;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
}

button:hover {
    background: linear-gradient(45deg, #00cc33, #00ff41);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 255, 65, 0.4);
}

button:active {
    transform: translateY(0);
}

/* Specific Puzzle Styles */
.binary-display {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    font-weight: 700;
    color: #00ff41;
    text-align: center;
    background: rgba(0, 0, 0, 0.5);
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
    letter-spacing: 5px;
    text-shadow: 0 0 10px #00ff41;
}

.fake-webpage {
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    font-family: Arial, sans-serif;
}

.fake-webpage h3 {
    color: #ffffff;
    margin-bottom: 10px;
}

.fake-webpage p {
    color: #ff6b6b;
    margin-bottom: 15px;
}

/* Login-related styles removed as login functionality has been disabled */

.code-error {
    background: #2a1a1a;
    border: 1px solid #ff6b6b;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    font-family: 'Roboto Mono', monospace;
}

.code-error pre {
    background: #1a1a1a;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
    margin: 10px 0;
}

.code-error code {
    color: #00ff41;
    line-height: 1.4;
}

.trivia-questions {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin: 20px 0;
}

.question {
    background: rgba(0, 0, 0, 0.3);
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #00ff41;
}

.question p {
    margin-bottom: 10px;
    color: #ffffff;
}

.question input {
    width: 100px;
}

.qr-fragments {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.qr-piece {
    background: #000;
    color: #fff;
    padding: 10px;
    border: 2px solid #00ff41;
    border-radius: 5px;
    font-family: monospace;
    cursor: move;
    user-select: none;
    transition: all 0.3s ease;
    touch-action: none; /* Prevent scrolling on mobile */
}

.qr-piece:hover {
    background: #00ff41;
    color: #000;
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0, 255, 65, 0.4);
}

.qr-piece:active {
    transform: scale(0.95);
}

.qr-piece.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.qr-assembly-area {
    min-height: 120px;
    border: 2px dashed #666;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 20px 0;
    color: #666;
    padding: 20px;
    transition: all 0.3s ease;
    text-align: center;
}

.qr-assembly-area.drag-over {
    border-color: #00ff41;
    background-color: rgba(0, 255, 65, 0.1);
    color: #00ff41;
}

.cipher-section {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #333;
}

.cipher-text {
    font-family: 'Orbitron', monospace;
    color: #ff6b6b;
    font-weight: 700;
}

.code-block {
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    overflow-x: auto;
}

.code-block pre {
    color: #00ff41;
    font-family: 'Roboto Mono', monospace;
    line-height: 1.5;
}

.riddle-text {
    background: rgba(0, 255, 65, 0.1);
    border: 1px solid #00ff41;
    border-radius: 10px;
    padding: 25px;
    margin: 20px 0;
    text-align: center;
}

.riddle-text em {
    font-size: 1.2rem;
    line-height: 1.8;
    color: #ffffff;
}

/* Gate Combination Pad Styles */
.gate-scenario {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid #333;
    border-radius: 10px;
    padding: 25px;
    margin: 20px 0;
}

.gate-inputs, .gate-operations, .gate-rule {
    margin-bottom: 25px;
}

.gate-inputs h3, .gate-operations h3, .gate-rule h3 {
    color: #00ff41;
    font-family: 'Orbitron', monospace;
    font-size: 1.2rem;
    margin-bottom: 15px;
    text-align: center;
}

.input-values {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.input-var {
    background: rgba(0, 255, 65, 0.1);
    border: 1px solid #00ff41;
    border-radius: 8px;
    padding: 10px 20px;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    color: #00ff41;
    font-size: 1.1rem;
}

.gate-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.gate-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid #444;
    border-radius: 8px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: space-between;
}

.gate-name {
    color: #ff6b6b;
    font-weight: 700;
    font-family: 'Orbitron', monospace;
    min-width: 60px;
}

.gate-formula {
    color: #ffffff;
    font-family: 'Roboto Mono', monospace;
    font-weight: 500;
    flex: 1;
    text-align: center;
}

.gate-calculation {
    color: #cccccc;
    font-family: 'Roboto Mono', monospace;
    min-width: 120px;
    text-align: right;
}

.result {
    color: #00ff41;
    font-weight: 700;
    font-size: 1.1rem;
}

.gate-rule {
    background: rgba(255, 107, 107, 0.1);
    border: 1px solid #ff6b6b;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
}

.gate-rule em {
    color: #ff6b6b;
    font-size: 1.1rem;
    font-weight: 500;
}

/* ASCII Puzzle Styles */
.anna-cryptic-message {
    background: rgba(255, 107, 107, 0.1);
    border: 1px solid #ff6b6b;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    text-align: center;
}

.anna-cryptic-message em {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #ff6b6b;
    font-style: italic;
}

.ascii-display {
    background: rgba(0, 0, 0, 0.5);
    border: 2px solid #00ff41;
    border-radius: 10px;
    padding: 25px;
    margin: 25px 0;
    text-align: center;
}

.ascii-numbers {
    font-family: 'Orbitron', monospace;
    font-size: 1.8rem;
    font-weight: 700;
    color: #00ff41;
    letter-spacing: 3px;
    margin-bottom: 20px;
    text-shadow: 0 0 10px #00ff41;
    word-break: break-all;
    line-height: 1.4;
}

.ascii-hint {
    border-top: 1px solid #333;
    padding-top: 15px;
    margin-top: 15px;
}

.ascii-hint em {
    font-size: 1rem;
    color: #cccccc;
    font-style: italic;
}

/* Victory Screen */
.victory-content {
    text-align: center;
    padding: 40px;
}

.victory-content h2 {
    font-family: 'Orbitron', monospace;
    font-size: 3rem;
    color: #00ff41;
    margin-bottom: 20px;
    text-shadow: 0 0 30px #00ff41;
}

.victory-content p {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 30px;
    color: #ffffff;
}

/* Hint System */
.hint-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.hint-button {
    background: rgba(255, 107, 107, 0.9);
    border: none;
    border-radius: 50px;
    padding: 15px 20px;
    color: #fff;
    font-size: 1rem;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.hint-content {
    position: absolute;
    bottom: 60px;
    right: 0;
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid #ff6b6b;
    border-radius: 10px;
    padding: 15px;
    max-width: 300px;
    display: none;
    color: #ffffff;
}

/* Success Animation */
.puzzle-container.success {
    animation: successPulse 1s ease-in-out;
}

@keyframes successPulse {
    0% { border-color: #333; }
    50% {
        border-color: #00ff41;
        box-shadow: 0 0 30px rgba(0, 255, 65, 0.5);
    }
    100% { border-color: #333; }
}

/* Error Animation */
.puzzle-container.error {
    animation: errorShake 0.5s ease-in-out;
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Matrix Rain Effect for Background */
.matrix-rain {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    opacity: 0.1;
}

/* Enhanced QR Assembly */
.qr-assembly-area.assembled {
    background: rgba(0, 255, 65, 0.1);
    border-color: #00ff41;
    color: #00ff41;
}

/* Glitch Effect for A.N.N.A. */
.anna-avatar.glitch {
    animation: glitch 0.3s ease-in-out;
}

@keyframes glitch {
    0% { transform: translate(0); }
    20% { transform: translate(-2px, 2px); }
    40% { transform: translate(-2px, -2px); }
    60% { transform: translate(2px, 2px); }
    80% { transform: translate(2px, -2px); }
    100% { transform: translate(0); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-title {
        font-size: 1.8rem;
    }

    .anna-dialogue {
        flex-direction: column;
        text-align: center;
    }

    .qr-fragments {
        justify-content: center;
    }

    input[type="text"], input[type="number"] {
        width: 150px;
        margin-bottom: 10px;
    }

    .trivia-questions {
        gap: 15px;
    }

    .binary-display {
        font-size: 2rem;
        letter-spacing: 3px;
    }

    .riddle-text em {
        font-size: 1rem;
    }
}
