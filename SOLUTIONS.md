# Tech Escape Room - Complete Solutions Guide

Welcome to the complete solutions guide for the Tech Escape Room! This document contains detailed solutions for all 8 puzzles across all 3 rounds.

## Overview

The Tech Escape Room features 8 different puzzles, each with 3 rounds (except puzzles 4 and 8 which have the same content across rounds). A.N.N.<PERSON>. (Automated Network Nodal Assistant) guides you through each challenge.

---

## Puzzle 1: Binary Gate
**Challenge**: Convert binary strings to decimal (after reversing them)

### Round 1
- **Binary**: `11010110`
- **Reversed**: `01101011`
- **Decimal**: `107`
- **Solution**: **107**

### Round 2
- **Binary**: `10110101`
- **Reversed**: `10101101`
- **Decimal**: `173`
- **Solution**: **173**

### Round 3
- **Binary**: `11001010`
- **Reversed**: `01010011`
- **Decimal**: `83`
- **Solution**: **83**

**How to solve**: 
1. Reverse the binary string
2. Convert the reversed binary to decimal
3. Example: `11010110` → `01101011` → (64+32+8+2+1) = 107

---

## Puzzle 2: Website Whisper
**Challenge**: Find hidden codes in webpage source code

### Round 1
- **Method**: Look for HTML comments in the webpage source
- **Hidden Code**: `CYBER2024` (found in comment: `<!-- SECRET_CODE: CYBER2024 -->`)
- **Solution**: **CYBER2024**

### Round 2
- **Method**: Look for debug keys in HTML comments
- **Hidden Code**: `HACK3R` (found in comment: `<!-- DEBUG_KEY: HACK3R -->`)
- **Solution**: **HACK3R**

### Round 3
- **Method**: Fix syntax error in JavaScript code to reveal the key
- **Code**: `if (key === undefined {` (missing closing parenthesis)
- **Fixed**: `if (key === undefined) {`
- **Hidden Code**: `C0D3BR34K` (revealed in the variable)
- **Solution**: **C0D3BR34K**

**How to solve**: 
- Right-click on the webpage and select "View Page Source" or "Inspect Element"
- Look for HTML comments (`<!-- -->`) containing secret codes
- In Round 3, fix the JavaScript syntax error to reveal the key

---

## Puzzle 3: Tech Trivia Lock
**Challenge**: Answer computer science trivia questions

### Round 1
1. **Q**: How many bits are in a byte? **A**: `8`
2. **Q**: What is the base of the hexadecimal number system? **A**: `16`
3. **Q**: What is 2⁵ (2 to the power of 5)? **A**: `32`

### Round 2
1. **Q**: How many bytes are in a kilobyte? **A**: `1024`
2. **Q**: What is the binary representation of decimal 15? **A**: `1111`
3. **Q**: How many bits are in a nibble? **A**: `4`

### Round 3
1. **Q**: What port does HTTP typically use? **A**: `80`
2. **Q**: How many possible values can 1 byte represent? **A**: `256`
3. **Q**: What is 16 in binary? **A**: `10000`

**How to solve**: Basic computer science knowledge required. All answers are numerical.

---

## Puzzle 4: Broken QR Code
**Challenge**: Decode a Caesar cipher from QR code fragments

- **QR Fragments**: `█▀▀█`, `█▄▄█`, `▀▀▀▀`, `████`
- **Encrypted Message**: `WHFK HVFDSH`
- **Caesar Shift**: 3 positions backward
- **Decryption**: W→T, H→E, F→C, K→H, etc.
- **Solution**: **TECH ESCAPE**

**How to solve**: 
1. The QR code fragments spell out the encrypted message
2. Apply Caesar cipher with shift of 3 (A→X, B→Y, C→Z, D→A, etc.)
3. WHFK HVFDSH → TECH ESCAPE

---

## Puzzle 5: Logic in Disguise
**Challenge**: Analyze JavaScript code and calculate the result

### Round 1
```javascript
let data = [12, 7, 23, 4, 18, 31, 9, 15];
let result = 0;
for (let i = 0; i < data.length; i++) {
    if (data[i] % 2 === 0) {  // Even numbers only
        result += data[i];
    }
}
```
- **Even numbers**: 12, 4, 18
- **Sum**: 12 + 4 + 18 = **34**

### Round 2
```javascript
let numbers = [5, 10, 15, 20, 25, 30];
let sum = 0;
for (let num of numbers) {
    if (num > 10) {  // Numbers greater than 10
        sum += num;
    }
}
```
- **Numbers > 10**: 15, 20, 25, 30
- **Sum**: 15 + 20 + 25 + 30 = **90**

### Round 3
```javascript
let arr = [3, 6, 9, 12, 15, 18];
let count = 0;
for (let i = 0; i < arr.length; i++) {
    if (arr[i] % 3 === 0 && arr[i] > 6) {  // Divisible by 3 AND > 6
        count += arr[i];
    }
}
```
- **Numbers divisible by 3 and > 6**: 9, 12, 15, 18
- **Sum**: 9 + 12 + 15 + 18 = **54**

**How to solve**: Trace through the code logic and calculate the mathematical result.

---

## Puzzle 6: Gate Combination Pad
**Challenge**: Determine which logic gate produces output 1

### Round 1
- **Inputs**: A=1, B=0, C=1
- **Gate 1 (AND)**: 1 AND 0 = 0
- **Gate 2 (OR)**: 0 OR 1 = 1 ✓
- **Gate 3 (XOR)**: 1 XOR 1 = 0
- **Solution**: **OR**

### Round 2
- **Inputs**: A=1, B=1, C=0
- **Gate 1 (AND)**: 1 AND 1 = 1 ✓
- **Gate 2 (OR)**: 1 OR 0 = 1 ✓
- **Gate 3 (XOR)**: 1 XOR 0 = 1 ✓
- **Solution**: **XOR** (the unique one that outputs 1)

### Round 3
- **Inputs**: A=0, B=1, C=1
- **Gate 1 (AND)**: 0 AND 1 = 0
- **Gate 2 (OR)**: 1 OR 1 = 1 ✓
- **Gate 3 (XOR)**: 0 XOR 1 = 1 ✓
- **Solution**: **OR**

**How to solve**: 
- AND: outputs 1 only if both inputs are 1
- OR: outputs 1 if either input is 1
- XOR: outputs 1 if exactly one input is 1

---

## Puzzle 7: ASCII Lock
**Challenge**: Convert ASCII numbers to letters

### Round 1
- **Numbers**: `83 101 114 118 101 114 32 69 115 99 97 112 101`
- **ASCII Conversion**: S-e-r-v-e-r-[space]-E-s-c-a-p-e
- **Solution**: **SERVER ESCAPE**

### Round 2
- **Numbers**: `72 65 67 75 69 82 32 77 79 68 69`
- **ASCII Conversion**: H-A-C-K-E-R-[space]-M-O-D-E
- **Solution**: **HACKER MODE**

### Round 3
- **Numbers**: `67 89 66 69 82 32 87 79 82 76 68`
- **ASCII Conversion**: C-Y-B-E-R-[space]-W-O-R-L-D
- **Solution**: **CYBER WORLD**

**How to solve**: 
- Each number represents an ASCII character
- 65=A, 66=B, 67=C, etc.
- 32 = space character
- Convert each number to its corresponding letter

---

## Puzzle 8: Localhost Escape
**Challenge**: Solve the networking riddle

**Riddle**:
```
"I am the address that points to self,
The loopback that no firewall can block,
Home to every machine's own health,
The one IP that's always in stock.
What am I?"
```

**Solution**: **127.0.0.1**

**How to solve**: 
- The riddle describes the localhost/loopback IP address
- 127.0.0.1 is the standard IPv4 loopback address
- It always points to the local machine ("self")
- No firewall blocks it because it's internal traffic

---

## Tips for Success

1. **Use the hints**: Click the 💡 HINT button if you're stuck
2. **Inspect the source**: For Puzzle 2, always check the HTML source code
3. **Think step by step**: For logic puzzles, trace through each operation
4. **Know your basics**: Brush up on binary, ASCII, and networking fundamentals
5. **Try different approaches**: Some puzzles have multiple valid answer formats

## A.N.N.A.'s Messages

- **Welcome**: "Welcome, intruder. I am A.N.N.A. - Automated Network Nodal Assistant..."
- **Victory**: "Remarkable. You have bested my security protocols. Your technical prowess is... adequate."

Good luck escaping A.N.N.A.'s network! 🚀
