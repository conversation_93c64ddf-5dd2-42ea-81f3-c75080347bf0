import React from 'react';
import BinaryGate from './puzzles/BinaryGate';
import WebsiteWhisper from './puzzles/WebsiteWhisper';
import TechTriviaLock from './puzzles/TechTriviaLock';
import BrokenQR from './puzzles/BrokenQR';
import LogicInDisguise from './puzzles/LogicInDisguise';
import GateCombinationPad from './puzzles/GateCombinationPad';
import AsciiLock from './puzzles/AsciiLock';
import LocalhostEscape from './puzzles/LocalhostEscape';

const PuzzleContainer = ({ 
  puzzleNumber, 
  puzzleData, 
  gameRound, 
  onSubmit,
  userAnswers,
  setUserAnswers
}) => {
  
  const renderPuzzle = () => {
    switch (puzzleNumber) {
      case 1:
        return (
          <BinaryGate
            puzzleData={puzzleData}
            onSubmit={() => onSubmit(puzzleNumber, userAnswers[1])}
            userAnswer={userAnswers[1] || ''}
            setUserAnswer={(val) => setUserAnswers(prev => ({ ...prev, 1: val }))}
          />
        );
      case 2:
        return (
          <WebsiteWhisper
            puzzleData={puzzleData}
            gameRound={gameRound}
            onSubmit={() => onSubmit(puzzleNumber, userAnswers[2])}
            userAnswer={userAnswers[2] || ''}
            setUserAnswer={(val) => setUserAnswers(prev => ({ ...prev, 2: val }))}
          />
        );
      case 3:
        return (
          <TechTriviaLock 
            puzzleData={puzzleData}
            onSubmit={() => onSubmit(puzzleNumber, userAnswers[3])}
            userAnswers={userAnswers[3] || []}
            setUserAnswers={(vals) => setUserAnswers(prev => ({ ...prev, 3: vals }))}
          />
        );
      case 4:
        return (
          <BrokenQR 
            puzzleData={puzzleData}
            onSubmit={() => onSubmit(puzzleNumber, userAnswers[4])}
            userAnswer={userAnswers[4] || ''}
            setUserAnswer={(val) => setUserAnswers(prev => ({ ...prev, 4: val }))}
          />
        );
      case 5:
        return (
          <LogicInDisguise 
            puzzleData={puzzleData}
            onSubmit={() => onSubmit(puzzleNumber, userAnswers[5])}
            userAnswer={userAnswers[5] || ''}
            setUserAnswer={(val) => setUserAnswers(prev => ({ ...prev, 5: val }))}
          />
        );
      case 6:
        return (
          <GateCombinationPad 
            puzzleData={puzzleData}
            onSubmit={() => onSubmit(puzzleNumber, userAnswers[6])}
            userAnswer={userAnswers[6] || ''}
            setUserAnswer={(val) => setUserAnswers(prev => ({ ...prev, 6: val }))}
          />
        );
      case 7:
        return (
          <AsciiLock 
            puzzleData={puzzleData}
            onSubmit={() => onSubmit(puzzleNumber, userAnswers[7])}
            userAnswer={userAnswers[7] || ''}
            setUserAnswer={(val) => setUserAnswers(prev => ({ ...prev, 7: val }))}
          />
        );
      case 8:
        return (
          <LocalhostEscape 
            puzzleData={puzzleData}
            onSubmit={() => onSubmit(puzzleNumber, userAnswers[8])}
            userAnswer={userAnswers[8] || ''}
            setUserAnswer={(val) => setUserAnswers(prev => ({ ...prev, 8: val }))}
          />
        );
      default:
        return <div>Puzzle not found</div>;
    }
  };

  return (
    <div
      className="puzzle-container active"
      id={`puzzle-${puzzleNumber}`}
    >
      {renderPuzzle()}
    </div>
  );
};

export default PuzzleContainer;
